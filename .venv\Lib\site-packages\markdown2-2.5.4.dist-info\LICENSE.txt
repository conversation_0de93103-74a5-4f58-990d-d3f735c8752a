This implementation of Markdown is licensed under the MIT License:

    The MIT License

    Copyright (c) 2012 Trent Mick
    Copyright (c) 2010 ActiveState Software Inc.

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the
    "Software"), to deal in the Software without restriction, including
    without limitation the rights to use, copy, modify, merge, publish,
    distribute, sublicense, and/or sell copies of the Software, and to permit
    persons to whom the Software is furnished to do so, subject to the
    following conditions:

    The above copyright notice and this permission notice shall be included
    in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN
    NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
    DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
    OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
    USE OR OTHER DEALINGS IN THE SOFTWARE.


All files in a *source package* of markdown2 (i.e. those available on
pypi.python.org and the Google Code project "downloads" page) are under the
MIT license.  However, in the *subversion repository* there are some files
(used for performance and testing purposes) that are under different licenses
as follows:

- perf/recipes.pprint

  Python License. This file includes a number of real-world examples of
  Markdown from the ActiveState Python Cookbook, used for doing some
  performance testing of markdown2.py.

- test/php-markdown-cases/...
  test/php-markdown-extra-cases/...

  GPL. These are from the MDTest package announced here:
  http://six.pairlist.net/pipermail/markdown-discuss/2007-July/000674.html

- test/markdown.py

  GPL 2 or BSD. A copy (currently old) of Python-Markdown -- the other
  Python Markdown implementation.

- test/markdown.php

  BSD-style. This is PHP Markdown
  (http://michelf.com/projects/php-markdown/).

- test/Markdown.pl: BSD-style

  A copy of Perl Markdown (http://daringfireball.net/projects/markdown/).

