# Word2Md2Html - Word 文档转换工具

一个强大的 Python 工具，可以将 Word 文档(.docx)转换为 Markdown 格式，并进一步转换为 HTML 格式。

## 功能特性

- ✅ 将 Word 文档转换为 Markdown 格式
- ✅ 将 Markdown 转换为 HTML 格式
- ✅ 保持文档的基本格式（粗体、斜体、下划线）
- ✅ 支持标题层级转换
- ✅ 支持表格转换
- ✅ 生成美观的 HTML 样式
- ✅ 支持中文内容
- ✅ 命令行界面，易于使用

## 安装要求

- Python 3.12+
- uv (推荐的包管理器)

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd word2md2html
```

### 2. 安装依赖

```bash
uv sync
```

### 3. 基本使用

```bash
# 转换Word文档为Markdown和HTML
uv run python main.py document.docx

# 只生成Markdown文件
uv run python main.py document.docx --md-only

# 只生成HTML文件
uv run python main.py document.docx --html-only

# 指定输出目录
uv run python main.py document.docx -o output_folder
```

## 命令行参数

- `input_file`: 输入的 Word 文档路径 (.docx 格式)
- `-o, --output`: 输出目录 (默认为当前目录)
- `--md-only`: 只生成 Markdown 文件
- `--html-only`: 只生成 HTML 文件

## 示例

### 创建示例文档

```bash
uv run python create_sample_docx.py
```

### 转换示例文档

```bash
uv run python main.py sample.docx
```

这将生成：

- `sample.md` - Markdown 格式文件
- `sample.html` - HTML 格式文件

## 支持的格式

### Word 文档格式

- 标题 (Heading 1-6)
- 段落文本
- 粗体文本
- 斜体文本
- 下划线文本
- 表格

### 输出格式

- **Markdown**: 标准 Markdown 格式，兼容 GitHub 等平台
- **HTML**: 包含 CSS 样式的完整 HTML 文档，适合直接在浏览器中查看

## 项目结构

```
word2md2html/
├── main.py                 # 主程序入口
├── word2md2html/          # 核心转换模块
│   ├── __init__.py
│   └── converter.py       # 转换器实现
├── create_sample_docx.py  # 示例文档生成器
├── pyproject.toml         # 项目配置
└── README.md             # 说明文档
```

## 技术实现

- **python-docx**: 读取 Word 文档
- **markdown2**: Markdown 到 HTML 转换
- **jinja2**: HTML 模板渲染
- **uv**: 现代 Python 包管理

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 许可证

MIT License
