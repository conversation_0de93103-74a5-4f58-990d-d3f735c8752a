#!/usr/bin/env python3
"""
Word转HTML批量转换GUI界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from word2md2html.simple_batch_converter import SimpleBatchConverter


class BatchWord2HtmlGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Word转HTML批量转换工具")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 设置样式
        style = ttk.Style()
        style.theme_use("clam")

        self.converter = SimpleBatchConverter()
        self.setup_callbacks()
        self.setup_ui()

        # 批量转换相关变量
        self.selected_files = []
        self.is_converting = False

    def setup_callbacks(self):
        """设置回调函数"""
        self.converter.set_progress_callback(self.on_progress)
        self.converter.set_error_callback(self.on_error)

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)

        # 标题
        title_label = ttk.Label(
            main_frame, text="Word转HTML批量转换工具", font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 转换模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="转换模式", padding="10")
        mode_frame.grid(
            row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10)
        )

        self.mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(
            mode_frame,
            text="单文件转换",
            variable=self.mode_var,
            value="single",
            command=self.on_mode_change,
        ).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(
            mode_frame,
            text="批量文件转换",
            variable=self.mode_var,
            value="batch",
            command=self.on_mode_change,
        ).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(
            mode_frame,
            text="目录转换",
            variable=self.mode_var,
            value="directory",
            command=self.on_mode_change,
        ).grid(row=0, column=2, sticky=tk.W)

        # 文件选择区域
        self.file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        self.file_frame.grid(
            row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10)
        )
        self.file_frame.columnconfigure(1, weight=1)

        # 单文件模式控件
        self.single_label = ttk.Label(self.file_frame, text="Word文件:")
        self.input_file_var = tk.StringVar()
        self.input_entry = ttk.Entry(
            self.file_frame, textvariable=self.input_file_var, width=50
        )
        self.browse_file_btn = ttk.Button(
            self.file_frame, text="浏览文件", command=self.browse_single_file
        )

        # 批量文件模式控件
        self.batch_label = ttk.Label(self.file_frame, text="选择的文件:")
        self.files_listbox = tk.Listbox(
            self.file_frame, height=4, selectmode=tk.EXTENDED
        )
        self.files_scrollbar = ttk.Scrollbar(
            self.file_frame, orient=tk.VERTICAL, command=self.files_listbox.yview
        )
        self.files_listbox.configure(yscrollcommand=self.files_scrollbar.set)
        self.browse_files_btn = ttk.Button(
            self.file_frame, text="选择文件", command=self.browse_multiple_files
        )
        self.remove_files_btn = ttk.Button(
            self.file_frame, text="移除选中", command=self.remove_selected_files
        )

        # 目录模式控件
        self.dir_label = ttk.Label(self.file_frame, text="输入目录:")
        self.input_dir_var = tk.StringVar()
        self.input_dir_entry = ttk.Entry(
            self.file_frame, textvariable=self.input_dir_var, width=50
        )
        self.browse_dir_btn = ttk.Button(
            self.file_frame, text="浏览目录", command=self.browse_input_dir
        )

        # 递归选项
        self.recursive_var = tk.BooleanVar(value=False)
        self.recursive_check = ttk.Checkbutton(
            self.file_frame, text="包含子目录", variable=self.recursive_var
        )

        # 输出目录
        ttk.Label(self.file_frame, text="输出目录:").grid(
            row=10, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0)
        )
        self.output_dir_var = tk.StringVar(value=str(Path.cwd() / "converted"))
        self.output_entry = ttk.Entry(
            self.file_frame, textvariable=self.output_dir_var, width=50
        )
        self.output_entry.grid(
            row=10, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0)
        )
        ttk.Button(self.file_frame, text="浏览", command=self.browse_output_dir).grid(
            row=10, column=2, pady=(10, 0)
        )

        # 转换选项
        options_frame = ttk.LabelFrame(main_frame, text="转换选项", padding="10")
        options_frame.grid(
            row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10)
        )

        self.generate_md_var = tk.BooleanVar(value=True)
        self.generate_html_var = tk.BooleanVar(value=True)

        ttk.Checkbutton(
            options_frame, text="生成Markdown文件", variable=self.generate_md_var
        ).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Checkbutton(
            options_frame, text="生成HTML文件", variable=self.generate_html_var
        ).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # 并发设置
        ttk.Label(options_frame, text="并发数:").grid(
            row=0, column=2, sticky=tk.W, padx=(20, 5)
        )
        self.max_workers_var = tk.IntVar(value=4)
        workers_spinbox = ttk.Spinbox(
            options_frame, from_=1, to=8, width=5, textvariable=self.max_workers_var
        )
        workers_spinbox.grid(row=0, column=3, sticky=tk.W)

        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(0, 10))

        self.convert_button = ttk.Button(
            button_frame,
            text="开始转换",
            command=self.start_conversion,
            style="Accent.TButton",
        )
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(
            button_frame,
            text="停止转换",
            command=self.stop_conversion,
            state="disabled",
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(
            side=tk.LEFT, padx=(0, 10)
        )
        ttk.Button(
            button_frame, text="打开输出目录", command=self.open_output_dir
        ).pack(side=tk.LEFT)

        # 进度显示
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(
            row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10)
        )
        progress_frame.columnconfigure(0, weight=1)

        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)

        self.progress_bar = ttk.Progressbar(progress_frame, mode="determinate")
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="10")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 初始化界面状态
        self.on_mode_change()

    def on_mode_change(self):
        """转换模式改变时的处理"""
        mode = self.mode_var.get()

        # 清空所有控件
        for widget in self.file_frame.winfo_children():
            widget.grid_remove()

        if mode == "single":
            # 单文件模式
            self.single_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
            self.input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
            self.browse_file_btn.grid(row=0, column=2)

        elif mode == "batch":
            # 批量文件模式
            self.batch_label.grid(row=0, column=0, sticky=(tk.W, tk.N), padx=(0, 10))
            self.files_listbox.grid(
                row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5)
            )
            self.files_scrollbar.grid(
                row=0, column=2, sticky=(tk.N, tk.S), padx=(0, 10)
            )

            # 使用grid而不是pack来管理按钮
            self.browse_files_btn.grid(row=1, column=1, sticky=tk.W, pady=(5, 0), padx=(0, 5))
            self.remove_files_btn.grid(row=1, column=2, sticky=tk.W, pady=(5, 0))

        elif mode == "directory":
            # 目录模式
            self.dir_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
            self.input_dir_entry.grid(
                row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10)
            )
            self.browse_dir_btn.grid(row=0, column=2)
            self.recursive_check.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        # 输出目录始终显示
        ttk.Label(self.file_frame, text="输出目录:").grid(
            row=10, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0)
        )
        self.output_entry.grid(
            row=10, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0)
        )
        ttk.Button(self.file_frame, text="浏览", command=self.browse_output_dir).grid(
            row=10, column=2, pady=(10, 0)
        )

    def browse_single_file(self):
        """浏览单个输入文件"""
        filename = filedialog.askopenfilename(
            title="选择Word文档",
            filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")],
        )
        if filename:
            self.input_file_var.set(filename)

    def browse_multiple_files(self):
        """浏览多个文件"""
        filenames = filedialog.askopenfilenames(
            title="选择Word文档",
            filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")],
        )
        if filenames:
            for filename in filenames:
                if filename not in self.selected_files:
                    self.selected_files.append(filename)
            self.update_files_listbox()

    def browse_input_dir(self):
        """浏览输入目录"""
        dirname = filedialog.askdirectory(title="选择输入目录")
        if dirname:
            self.input_dir_var.set(dirname)

    def browse_output_dir(self):
        """浏览输出目录"""
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_dir_var.set(dirname)

    def remove_selected_files(self):
        """移除选中的文件"""
        selected_indices = self.files_listbox.curselection()
        for index in reversed(selected_indices):
            del self.selected_files[index]
        self.update_files_listbox()

    def update_files_listbox(self):
        """更新文件列表框"""
        self.files_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            self.files_listbox.insert(tk.END, Path(file_path).name)

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def open_output_dir(self):
        """打开输出目录"""
        output_dir = Path(self.output_dir_var.get())
        if output_dir.exists():
            os.startfile(str(output_dir))
        else:
            messagebox.showwarning("警告", "输出目录不存在")

    def on_progress(self, message, current=0, total=0):
        """进度回调"""

        def update_ui():
            self.progress_var.set(message)
            if total > 0:
                progress_percent = (current / total) * 100
                self.progress_bar["value"] = progress_percent
            self.log_message(message)

        self.root.after(0, update_ui)

    def on_error(self, error, file_path=""):
        """错误回调"""

        def update_ui():
            error_msg = f"错误: {error}"
            if file_path:
                error_msg += f" (文件: {Path(file_path).name})"
            self.log_message(error_msg)

        self.root.after(0, update_ui)

    def start_conversion(self):
        """开始转换"""
        if self.is_converting:
            return

        mode = self.mode_var.get()

        # 验证输入
        if mode == "single":
            if not self.input_file_var.get():
                messagebox.showerror("错误", "请选择要转换的Word文件")
                return
        elif mode == "batch":
            if not self.selected_files:
                messagebox.showerror("错误", "请选择要转换的Word文件")
                return
        elif mode == "directory":
            if not self.input_dir_var.get():
                messagebox.showerror("错误", "请选择输入目录")
                return

        if not self.generate_md_var.get() and not self.generate_html_var.get():
            messagebox.showerror("错误", "请至少选择一种输出格式")
            return

        # 更新UI状态
        self.is_converting = True
        self.convert_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.progress_bar["value"] = 0

        # 在新线程中执行转换
        thread = threading.Thread(target=self.convert_files)
        thread.daemon = True
        thread.start()

    def stop_conversion(self):
        """停止转换"""
        self.is_converting = False
        self.conversion_finished()

    def convert_files(self):
        """执行文件转换"""
        try:
            mode = self.mode_var.get()
            output_dir = Path(self.output_dir_var.get())

            if mode == "single":
                input_file = Path(self.input_file_var.get())
                self.log_message(f"开始转换: {input_file.name}")

                result = self.converter.convert_single(
                    input_file=input_file,
                    output_dir=output_dir,
                    generate_md=self.generate_md_var.get(),
                    generate_html=self.generate_html_var.get(),
                )

                self.log_message("转换完成!")
                self.show_conversion_results(result)

            elif mode == "batch":
                self.log_message(f"开始批量转换 {len(self.selected_files)} 个文件...")

                results = self.converter.convert_batch(
                    input_files=self.selected_files,
                    output_dir=output_dir,
                    generate_md=self.generate_md_var.get(),
                    generate_html=self.generate_html_var.get(),
                    max_workers=self.max_workers_var.get(),
                )

                self.log_message("批量转换完成!")
                self.log_message(f"成功转换 {len(results)} 个文件")

            elif mode == "directory":
                input_dir = Path(self.input_dir_var.get())
                self.log_message(f"开始转换目录: {input_dir}")

                results = self.converter.convert_directory(
                    input_dir=input_dir,
                    output_dir=output_dir,
                    generate_md=self.generate_md_var.get(),
                    generate_html=self.generate_html_var.get(),
                    max_workers=self.max_workers_var.get(),
                    recursive=self.recursive_var.get(),
                )

                self.log_message("目录转换完成!")
                self.log_message(f"成功转换 {len(results)} 个文件")

            # 显示成功消息
            self.root.after(0, lambda: messagebox.showinfo("成功", "转换完成！"))

        except Exception as e:
            error_msg = f"转换失败: {str(e)}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

        finally:
            # 恢复UI状态
            self.root.after(0, self.conversion_finished)

    def show_conversion_results(self, result):
        """显示转换结果"""
        if "markdown_file" in result:
            self.log_message(f"Markdown文件: {result['markdown_file']}")

        if "html_file" in result:
            self.log_message(f"HTML文件: {result['html_file']}")

        if "book_directory" in result:
            self.log_message(f"书籍目录: {result['book_directory']}")
            chapter_count = len([k for k in result.keys() if "chapter_" in k])
            if chapter_count > 0:
                self.log_message(f"包含 {chapter_count} 个章节文件")

        self.log_message("-" * 50)

    def conversion_finished(self):
        """转换完成后的UI恢复"""
        self.is_converting = False
        self.convert_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.progress_bar["value"] = 0
        self.progress_var.set("就绪")


def main():
    """主函数"""
    root = tk.Tk()
    app = BatchWord2HtmlGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
