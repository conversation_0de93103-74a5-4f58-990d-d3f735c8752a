"""
创建示例Word文档用于测试
"""

from docx import Document
from docx.shared import Inches


def create_sample_document():
    """创建一个示例Word文档"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('Word转Markdown转HTML示例文档', 0)
    
    # 添加段落
    doc.add_paragraph('这是一个示例文档，用于测试Word到Markdown再到HTML的转换功能。')
    
    # 添加二级标题
    doc.add_heading('功能特性', level=1)
    
    # 添加列表内容
    doc.add_paragraph('本工具支持以下功能：')
    doc.add_paragraph('• 将Word文档转换为Markdown格式', style='List Bullet')
    doc.add_paragraph('• 将Markdown转换为HTML格式', style='List Bullet')
    doc.add_paragraph('• 保持文档的基本格式', style='List Bullet')
    doc.add_paragraph('• 支持表格转换', style='List Bullet')
    
    # 添加格式化文本
    doc.add_heading('文本格式', level=2)
    p = doc.add_paragraph('这段文字包含不同的格式：')
    p.add_run('粗体文字').bold = True
    p.add_run('，')
    p.add_run('斜体文字').italic = True
    p.add_run('，以及')
    p.add_run('下划线文字').underline = True
    p.add_run('。')
    
    # 添加表格
    doc.add_heading('示例表格', level=2)
    table = doc.add_table(rows=1, cols=3)
    table.style = 'Table Grid'
    
    # 表头
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = '姓名'
    hdr_cells[1].text = '年龄'
    hdr_cells[2].text = '职业'
    
    # 添加数据行
    row_cells = table.add_row().cells
    row_cells[0].text = '张三'
    row_cells[1].text = '25'
    row_cells[2].text = '程序员'
    
    row_cells = table.add_row().cells
    row_cells[0].text = '李四'
    row_cells[1].text = '30'
    row_cells[2].text = '设计师'
    
    # 添加结论
    doc.add_heading('总结', level=1)
    doc.add_paragraph('这个工具可以帮助您快速将Word文档转换为Web友好的格式。')
    
    # 保存文档
    doc.save('sample.docx')
    print("示例文档 'sample.docx' 已创建")


if __name__ == "__main__":
    create_sample_document()
