import argparse
from pathlib import Path
from word2md2html.converter import Word2Md2HtmlConverter


def main():
    parser = argparse.ArgumentParser(description="将Word文档转换为Markdown和HTML")
    parser.add_argument("input_file", help="输入的Word文档路径 (.docx)")
    parser.add_argument("-o", "--output", help="输出目录 (默认为当前目录)", default=".")
    parser.add_argument("--md-only", action="store_true", help="只生成Markdown文件")
    parser.add_argument("--html-only", action="store_true", help="只生成HTML文件")

    args = parser.parse_args()

    # 验证输入文件
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"错误: 文件 '{args.input_file}' 不存在")
        return 1

    if not input_path.suffix.lower() == ".docx":
        print(f"错误: 输入文件必须是 .docx 格式")
        return 1

    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 创建转换器
    converter = Word2Md2HtmlConverter()

    try:
        # 执行转换
        result = converter.convert(
            input_path,
            output_dir,
            generate_md=not args.html_only,
            generate_html=not args.md_only,
        )

        print("转换完成!")
        if result.get("markdown_file"):
            print(f"Markdown文件: {result['markdown_file']}")
        if result.get("html_file"):
            print(f"HTML文件: {result['html_file']}")

    except Exception as e:
        print(f"转换失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
