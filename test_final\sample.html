<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>sample</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        h1 { border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 0;
            padding-left: 20px;
            color: #7f8c8d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
<p>Word转Markdown转HTML示例文档</p>

<p>这是一个示例文档，用于测试Word到Markdown再到HTML的转换功能。</p>

<h1>功能特性</h1>

<p>本工具支持以下功能：</p>

<ul>
<li>将Word文档转换为Markdown格式</li>
<li>将Markdown转换为HTML格式</li>
<li>保持文档的基本格式</li>
<li>支持表格转换</li>
</ul>

<h2>文本格式</h2>

<p>这段文字包含不同的格式：<strong>粗体文字</strong>，<em>斜体文字</em>，以及_下划线文字_。</p>

<h2>示例表格</h2>

<h1>总结</h1>

<p>这个工具可以帮助您快速将Word文档转换为Web友好的格式。</p>

<table>
<thead>
<tr>
  <th>姓名</th>
  <th>年龄</th>
  <th>职业</th>
</tr>
</thead>
<tbody>
<tr>
  <td>张三</td>
  <td>25</td>
  <td>程序员</td>
</tr>
<tr>
  <td>李四</td>
  <td>30</td>
  <td>设计师</td>
</tr>
</tbody>
</table>

</body>
</html>