#!/usr/bin/env python3
"""
Word转HTML批量转换器
基于原始converter.py，增加批量处理功能
"""

import re
from pathlib import Path
from typing import Dict, List, Optional, Union
from jinja2 import Template
from docx import Document
import markdown2
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time

from .converter import Word2Md2HtmlConverter


class BatchWord2Md2HtmlConverter(Word2Md2HtmlConverter):
    """Word转Markdown和HTML的批量转换器"""

    def __init__(self):
        """初始化转换器"""
        super().__init__()
        self._lock = threading.Lock()
        self._progress_callback = None
        self._error_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self._progress_callback = callback

    def set_error_callback(self, callback):
        """设置错误回调函数"""
        self._error_callback = callback

    def _report_progress(self, message: str, current: int = 0, total: int = 0):
        """报告进度"""
        if self._progress_callback:
            self._progress_callback(message, current, total)

    def _report_error(self, error: str, file_path: str = ""):
        """报告错误"""
        if self._error_callback:
            self._error_callback(error, file_path)

    def convert_single(
        self,
        input_file: Union[str, Path],
        output_dir: Union[str, Path],
        generate_md: bool = True,
        generate_html: bool = True,
    ) -> Dict[str, str]:
        """转换单个文件（使用父类的convert方法）"""
        return self.convert(
            input_file=input_file,
            output_dir=output_dir,
            generate_md=generate_md,
            generate_html=generate_html,
        )

    def convert_batch(
        self,
        input_files: List[Union[str, Path]],
        output_dir: Union[str, Path],
        generate_md: bool = True,
        generate_html: bool = True,
        max_workers: int = 4,
    ) -> Dict[str, Dict[str, str]]:
        """批量转换多个文件"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        results = {}
        total_files = len(input_files)
        completed_files = 0

        self._report_progress(f"开始批量转换 {total_files} 个文件...", 0, total_files)

        # 使用线程池进行并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(
                    self._convert_single_with_error_handling,
                    input_file,
                    output_dir,
                    generate_md,
                    generate_html,
                ): input_file
                for input_file in input_files
            }

            # 处理完成的任务
            for future in as_completed(future_to_file):
                input_file = future_to_file[future]
                file_path = Path(input_file)

                try:
                    result = future.result()
                    results[str(file_path)] = result
                    completed_files += 1

                    with self._lock:
                        self._report_progress(
                            f"已完成: {file_path.name}",
                            completed_files,
                            total_files,
                        )

                except Exception as e:
                    with self._lock:
                        self._report_error(str(e), str(file_path))
                        completed_files += 1
                        self._report_progress(
                            f"失败: {file_path.name}",
                            completed_files,
                            total_files,
                        )

        self._report_progress("批量转换完成!", total_files, total_files)
        return results

    def _convert_single_with_error_handling(
        self, input_file, output_dir, generate_md, generate_html
    ):
        """带错误处理的单文件转换"""
        try:
            return self.convert_single(
                input_file, output_dir, generate_md, generate_html
            )
        except Exception as e:
            raise Exception(f"转换失败: {e}")

    def convert_directory(
        self,
        input_dir: Union[str, Path],
        output_dir: Union[str, Path],
        pattern: str = "*.docx",
        generate_md: bool = True,
        generate_html: bool = True,
        max_workers: int = 4,
        recursive: bool = False,
    ) -> Dict[str, Dict[str, str]]:
        """转换目录中的所有Word文件"""
        input_dir = Path(input_dir)

        if not input_dir.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")

        # 查找所有Word文件
        if recursive:
            input_files = list(input_dir.rglob(pattern))
        else:
            input_files = list(input_dir.glob(pattern))

        if not input_files:
            raise ValueError(f"在目录 {input_dir} 中没有找到匹配 {pattern} 的文件")

        self._report_progress(f"在目录中找到 {len(input_files)} 个文件")

        return self.convert_batch(
            input_files, output_dir, generate_md, generate_html, max_workers
        )

        # 以下方法继承自父类，不需要重复定义
        # def _markdown_to_html(self, markdown_content: str, title: str) -> str:
        """将Markdown转换为HTML"""
        html_body = markdown2.markdown(
            markdown_content,
            extras=[
                "fenced-code-blocks",
                "tables",
                "strike",
                "task_list",
                "code-friendly",
            ],
        )

        template = Template(self.single_template)
        return template.render(
            title=title, css_content=self.base_css, content=html_body
        )

    def _has_multiple_chapters(self, markdown_content: str) -> bool:
        """检查是否包含多篇信息或多章内容"""
        # 查找是否有"第X篇"、"第X章"、"说明"、"序"、"简介"等模式
        pattern_chapters = r"^# 第.+篇"
        pattern_sections = r"^# 第.+章"
        pattern_intro = r"^# (说明|序|简介|前言|导言)"

        chapters_matches = re.findall(pattern_chapters, markdown_content, re.MULTILINE)
        sections_matches = re.findall(pattern_sections, markdown_content, re.MULTILINE)
        intro_matches = re.findall(pattern_intro, markdown_content, re.MULTILINE)

        # 如果有说明/序/简介等，加上篇章数量大于等于1就分割
        # 如果没有说明等，篇章数量大于1才分割
        total_chapters = len(chapters_matches) + len(sections_matches)
        if len(intro_matches) > 0:
            return total_chapters >= 1
        else:
            return total_chapters > 1

    def _generate_split_html(
        self, markdown_content: str, base_name: str, full_name: str, output_dir: Path
    ) -> Dict[str, str]:
        """分割生成多个HTML文件"""

        # 创建以文件名为名的子目录
        book_dir = output_dir / base_name
        book_dir.mkdir(parents=True, exist_ok=True)

        result = {}

        # 分割内容
        sections = self._split_markdown_by_chapters(markdown_content)

        # 提取纯书名（去掉书号和作者）
        book_title = self._extract_book_title(full_name)

        # 生成目录页面
        index_content = self._generate_index_html(sections, base_name, book_title)
        index_file = book_dir / "index.htm"
        with open(index_file, "w", encoding="utf-8") as f:
            f.write(index_content)
        result["index_file"] = str(index_file)

        # 检查是否有真正的intro section
        has_real_intro = any(
            section["type"] == "intro" and self._has_real_intro_content(section)
            for section in sections
        )

        # 生成各个章节的HTML文件
        for i, section in enumerate(sections):
            if section["type"] == "intro" and self._has_real_intro_content(section):
                # 只有真正的intro才生成00.htm
                filename = "00.htm"
                nav_content = (
                    f'上一篇 <a href="index.htm">回目录</a> <a href="01.htm">下一篇</a>'
                )
                bottom_nav_content = f'上一篇 <a href="index.htm">回目录</a> <a href="#top">回页首</a> <a href="01.htm">下一篇</a>'

                html_content = self._generate_chapter_html(
                    section, nav_content, bottom_nav_content
                )
                html_file = book_dir / filename
                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(html_content)
                result[f"intro_file"] = str(html_file)

            elif section["type"] == "chapter":
                # 第X篇生成为XX.htm
                filename = f"{section['number']:02d}.htm"

                # 根据是否有真正的intro来决定导航链接
                if has_real_intro:
                    prev_file = (
                        f"{section['number']-1:02d}.htm"
                        if section["number"] > 1
                        else "00.htm"
                    )
                else:
                    prev_file = (
                        f"{section['number']-1:02d}.htm"
                        if section["number"] > 1
                        else None  # 第一篇没有上一篇
                    )

                next_file = (
                    f"{section['number']+1:02d}.htm" if i < len(sections) - 1 else ""
                )

                # 生成导航内容
                prev_link = (
                    f'<a href="{prev_file}">上一篇</a>' if prev_file else "上一篇"
                )
                next_link = (
                    f'<a href="{next_file}">下一篇</a>' if next_file else "下一篇"
                )

                nav_content = f'{prev_link} <a href="index.htm">回目录</a> {next_link}'
                bottom_nav_content = f'{prev_link} <a href="index.htm">回目录</a> <a href="#top">回页首</a> {next_link}'

                html_content = self._generate_chapter_html(
                    section, nav_content, bottom_nav_content
                )
                html_file = book_dir / filename
                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(html_content)
                result[f"chapter_{section['number']}_file"] = str(html_file)

        result["book_directory"] = str(book_dir)
        return result

    def _split_markdown_by_chapters(self, markdown_content: str) -> list:
        """按章节分割Markdown内容"""

        lines = markdown_content.split("\n")
        sections = []
        current_section = {"type": "intro", "title": "说明", "content": "", "number": 0}

        for line in lines:
            # 检查是否是章节标题（篇或章）
            chapter_match = re.match(r"^# 第(.+)篇[　\s]*(.+)", line)
            section_match = re.match(r"^# 第(.+)章[　\s]*(.+)", line)
            intro_match = re.match(r"^# (说明|序|简介|前言|导言)(.*)$", line)

            if chapter_match:
                # 保存前一个章节
                if current_section["content"].strip():
                    sections.append(current_section)

                # 开始新章节（篇）
                chapter_num_text = chapter_match.group(1)
                chapter_title = chapter_match.group(2)

                # 转换中文数字为阿拉伯数字
                chapter_num = self._chinese_to_number(chapter_num_text)

                current_section = {
                    "type": "chapter",
                    "title": f"第{chapter_num_text}篇　{chapter_title}",
                    "content": line + "\n",
                    "number": chapter_num,
                }
            elif section_match:
                # 保存前一个章节
                if current_section["content"].strip():
                    sections.append(current_section)

                # 开始新章节（章）
                section_num_text = section_match.group(1)
                section_title = section_match.group(2)

                # 转换中文数字为阿拉伯数字
                section_num = self._chinese_to_number(section_num_text)

                current_section = {
                    "type": "chapter",
                    "title": f"第{section_num_text}章　{section_title}",
                    "content": line + "\n",
                    "number": section_num,
                }
            elif intro_match:
                # 如果当前section是默认的intro且内容很少，就合并到新的intro中
                if (
                    current_section["type"] == "intro"
                    and len(current_section["content"].strip()) < 50
                ):
                    # 合并内容到新的intro中
                    intro_type = intro_match.group(1)
                    intro_subtitle = intro_match.group(2).strip()

                    title = intro_type
                    if intro_subtitle:
                        title = f"{intro_type}　{intro_subtitle}"

                    current_section = {
                        "type": "intro",
                        "title": title,
                        "content": current_section["content"] + line + "\n",
                        "number": 0,
                    }
                else:
                    # 保存前一个章节
                    if current_section["content"].strip():
                        sections.append(current_section)

                    # 开始新的说明/序/简介章节
                    intro_type = intro_match.group(1)
                    intro_subtitle = intro_match.group(2).strip()

                    title = intro_type
                    if intro_subtitle:
                        title = f"{intro_type}　{intro_subtitle}"

                    current_section = {
                        "type": "intro",
                        "title": title,
                        "content": line + "\n",
                        "number": 0,
                    }
            else:
                current_section["content"] += line + "\n"

        # 添加最后一个章节
        if current_section["content"].strip():
            sections.append(current_section)

        return sections

    def _chinese_to_number(self, chinese_num: str) -> int:
        """将中文数字转换为阿拉伯数字"""
        chinese_map = {
            "一": 1,
            "二": 2,
            "三": 3,
            "四": 4,
            "五": 5,
            "六": 6,
            "七": 7,
            "八": 8,
            "九": 9,
            "十": 10,
        }
        return chinese_map.get(chinese_num, 1)

    def _extract_book_title(self, full_name: str) -> str:
        """从完整文件名中提取纯书名"""
        # 去掉书号（开头的数字）
        title_without_number = re.sub(r"^\d+", "", full_name)

        # 去掉作者（最后的"－作者"部分）
        title_without_author = re.sub(r"[－—-][^－—-]*$", "", title_without_number)

        # 清理可能的多余字符
        clean_title = title_without_author.strip("－—-")

        return clean_title if clean_title else full_name

    def _has_real_intro_content(self, section: dict) -> bool:
        """检查是否有真正的intro内容（不是默认生成的）"""
        if section["type"] != "intro":
            return False

        # 检查标题是否是明确的intro标题（不是默认的"说明"）
        title = section.get("title", "")
        if title in ["序", "简介", "前言", "导言"]:
            return True

        # 检查内容中是否包含明确的intro标记
        content = section.get("content", "").strip()

        # 如果内容为空或只有很少内容，认为不是真正的intro
        if len(content) < 20:
            return False

        intro_patterns = [
            "主后一九",
            "本书是由",
            "这是本书",
            "序言",
            "前言",
            "导言",
            "说明",
        ]

        # 必须包含intro标记才认为是真正的intro
        return any(pattern in content for pattern in intro_patterns)

    def _generate_index_html(
        self, sections: list, base_name: str, book_title: str
    ) -> str:
        """生成目录页面"""
        toc_lines = []
        intro_added = False

        for section in sections:
            if (
                section["type"] == "intro"
                and not intro_added
                and self._has_real_intro_content(section)
            ):
                intro_title = section.get("title", "说明")
                toc_lines.append(f'<p><a href="00.htm">{intro_title}</a></p>')
                intro_added = True
            elif section["type"] == "chapter":
                title = section["title"]
                # 提取篇数/章数和标题
                chapter_match = re.match(r"第(.+)篇[　\s]*(.+)", title)
                section_match = re.match(r"第(.+)章[　\s]*(.+)", title)

                if chapter_match:
                    chapter_num_text = chapter_match.group(1)
                    chapter_title = chapter_match.group(2)
                    filename = f"{section['number']:02d}.htm"
                    toc_lines.append(
                        f'<p>第　{chapter_num_text}篇　<a href="{filename}">{chapter_title}</a></p>'
                    )
                elif section_match:
                    section_num_text = section_match.group(1)
                    section_title = section_match.group(2)
                    filename = f"{section['number']:02d}.htm"
                    toc_lines.append(
                        f'<p>第　{section_num_text}章　<a href="{filename}">{section_title}</a></p>'
                    )

        toc_content = "\n".join(toc_lines)

        template = Template(self.index_template)
        return template.render(
            title=book_title,
            book_title=book_title,
            author="李常受",
            css_content=self.base_css,
            toc_content=toc_content,
        )

    def _generate_chapter_html(
        self, section: dict, nav_content: str, bottom_nav_content: str
    ) -> str:
        """生成章节HTML内容"""
        # 将Markdown转换为HTML
        markdown_content = section["content"]

        # 清理内容
        lines = markdown_content.split("\n")
        cleaned_lines = []

        # 对于intro section，找到实际内容的开始位置
        start_processing = True
        if section["type"] == "intro":
            start_processing = False

        for line in lines:
            line = line.strip()

            # 跳过标题行，因为模板会添加
            if line.startswith("# "):
                if section["type"] == "intro":
                    start_processing = True  # 从说明标题开始处理
                continue

            # 对于intro，只有开始处理后才添加内容
            if section["type"] == "intro" and not start_processing:
                continue

            # 跳过通用的不需要内容
            if line == "目录" or line == "************":
                continue

            # 保留空行以维持段落分割
            if line == "":
                cleaned_lines.append("")
            else:
                cleaned_lines.append(line)

        content_without_title = "\n".join(cleaned_lines).strip()

        # 转换为HTML
        html_body = markdown2.markdown(
            content_without_title,
            extras=[
                "fenced-code-blocks",
                "tables",
                "strike",
                "task_list",
                "code-friendly",
            ],
        )

        # 将内容中的h2标题转换为h3，因为篇题已经使用h2
        html_body = html_body.replace("<h2>", "<h3>").replace("</h2>", "</h3>")

        template = Template(self.chapter_template)
        return template.render(
            title=section["title"],
            css_content=self.base_css,
            nav_content=nav_content,
            bottom_nav_content=bottom_nav_content,
            content=html_body,
        )
