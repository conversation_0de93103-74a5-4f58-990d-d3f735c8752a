"""
Word文档转换器模块
支持将Word文档转换为Markdown和HTML格式
"""

import re
from pathlib import Path
from typing import Dict, Optional
from docx import Document
from docx.shared import Inches
import markdown2
from jinja2 import Template


class Word2Md2HtmlConverter:
    """Word文档转换器类"""

    def __init__(self):
        self.html_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        h1 { border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 0;
            padding-left: 20px;
            color: #7f8c8d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
{{ content }}
</body>
</html>"""

    def convert(
        self,
        input_file: Path,
        output_dir: Path,
        generate_md: bool = True,
        generate_html: bool = True,
    ) -> Dict[str, Optional[str]]:
        """
        转换Word文档为Markdown和/或HTML

        Args:
            input_file: 输入的Word文档路径
            output_dir: 输出目录
            generate_md: 是否生成Markdown文件
            generate_html: 是否生成HTML文件

        Returns:
            包含生成文件路径的字典
        """
        # 读取Word文档
        doc = Document(input_file)

        # 转换为Markdown
        markdown_content = self._docx_to_markdown(doc)

        # 生成文件名
        base_name = input_file.stem

        result = {}

        # 保存Markdown文件
        if generate_md:
            md_file = output_dir / f"{base_name}.md"
            with open(md_file, "w", encoding="utf-8") as f:
                f.write(markdown_content)
            result["markdown_file"] = str(md_file)

        # 生成HTML文件
        if generate_html:
            html_content = self._markdown_to_html(markdown_content, base_name)
            html_file = output_dir / f"{base_name}.html"
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(html_content)
            result["html_file"] = str(html_file)

        return result

    def _docx_to_markdown(self, doc: Document) -> str:
        """将Word文档转换为Markdown格式"""
        markdown_lines = []
        prev_was_list = False

        for paragraph in doc.paragraphs:
            # 跳过空段落
            if not paragraph.text.strip():
                markdown_lines.append("")
                prev_was_list = False
                continue

            # 处理标题
            if paragraph.style.name.startswith("Heading"):
                # 如果前一个是列表项，添加空行来结束列表
                if prev_was_list:
                    markdown_lines.append("")
                level = int(paragraph.style.name.split()[-1])
                markdown_lines.append(f"{'#' * level} {paragraph.text}")
                markdown_lines.append("")  # 标题后添加空行
                prev_was_list = False
            elif paragraph.style.name.startswith(
                "List"
            ) or paragraph.text.strip().startswith(("•", "-", "*")):
                # 处理列表项
                text = self._process_paragraph_formatting(paragraph)
                # 移除开头的列表符号，因为我们会添加markdown格式的符号
                text = text.lstrip("•-* \t")
                markdown_lines.append(f"- {text}")
                prev_was_list = True
            else:
                # 如果前一个是列表项，添加空行来结束列表
                if prev_was_list:
                    markdown_lines.append("")
                # 处理普通段落
                text = self._process_paragraph_formatting(paragraph)
                markdown_lines.append(text)
                markdown_lines.append("")  # 段落后添加空行
                prev_was_list = False

        # 处理表格
        for table in doc.tables:
            markdown_lines.extend(self._process_table(table))
            markdown_lines.append("")  # 表格后添加空行

        # 清理多余的空行
        cleaned_lines = []
        prev_empty = False
        for line in markdown_lines:
            if line.strip() == "":
                if not prev_empty:
                    cleaned_lines.append("")
                prev_empty = True
            else:
                cleaned_lines.append(line)
                prev_empty = False

        return "\n".join(cleaned_lines)

    def _process_paragraph_formatting(self, paragraph) -> str:
        """处理段落中的格式化文本"""
        text_parts = []

        for run in paragraph.runs:
            text = run.text

            # 处理粗体
            if run.bold:
                text = f"**{text}**"

            # 处理斜体
            if run.italic:
                text = f"*{text}*"

            # 处理下划线（转换为强调）
            if run.underline:
                text = f"_{text}_"

            text_parts.append(text)

        return "".join(text_parts)

    def _process_table(self, table) -> list:
        """处理表格转换为Markdown格式"""
        markdown_table = []

        for i, row in enumerate(table.rows):
            row_cells = [cell.text.strip() for cell in row.cells]
            markdown_row = "| " + " | ".join(row_cells) + " |"
            markdown_table.append(markdown_row)

            # 添加表头分隔符
            if i == 0:
                separator = "| " + " | ".join(["---"] * len(row_cells)) + " |"
                markdown_table.append(separator)

        return markdown_table

    def _markdown_to_html(self, markdown_content: str, title: str) -> str:
        """将Markdown转换为HTML"""
        # 使用markdown2转换
        html_body = markdown2.markdown(
            markdown_content,
            extras=[
                "fenced-code-blocks",
                "tables",
                "strike",
                "task_list",
                "code-friendly",
            ],
        )

        # 使用模板生成完整HTML
        template = Template(self.html_template)
        return template.render(title=title, content=html_body)
