"""
Word文档转换器模块
支持将Word文档转换为Markdown和HTML格式
"""

import re
from pathlib import Path
from typing import Dict, Optional
from docx import Document
import markdown2
from jinja2 import Template


class Word2Md2HtmlConverter:
    """Word文档转换器类"""

    def __init__(self):
        # 读取书籍模板的CSS样式
        self.base_css = self._read_template_css()

        # 书籍风格的HTML模板
        self.html_template = """<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="Publisher" content="台湾福音书房" />
<meta name="Author" content="{{ author }}" />
<style>
{{ css_content }}
</style>
<title>{{ title }}</title>
</head>
<body>
{{ content }}
</body>
</html>"""

        # 目录页面模板
        self.index_template = """<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="Publisher" content="台湾福音书房" />
<meta name="Author" content="{{ author }}" />
<style>
{{ css_content }}
</style>
<title>{{ title }}</title>
</head>
<body>
<h1>{{ book_title }}</h1>

{{ toc_content }}

</body>
</html>"""

        # 章节页面模板
        self.chapter_template = """<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<style>
{{ css_content }}
</style>
<title>{{ title }}</title>
</head>
<body>
<a name="top"></a><h2>{{ title }}</h2>
<div class="nav">{{ nav_content }}</div>
<hr />

{{ content }}

<hr />
<div class="nav">{{ bottom_nav_content }}</div>
</body>
</html>"""

    def convert(
        self,
        input_file: Path,
        output_dir: Path,
        generate_md: bool = True,
        generate_html: bool = True,
    ) -> Dict[str, Optional[str]]:
        """
        转换Word文档为Markdown和/或HTML

        Args:
            input_file: 输入的Word文档路径
            output_dir: 输出目录
            generate_md: 是否生成Markdown文件
            generate_html: 是否生成HTML文件

        Returns:
            包含生成文件路径的字典
        """
        # 读取Word文档
        doc = Document(input_file)

        # 转换为Markdown
        markdown_content = self._docx_to_markdown(doc)

        # 生成文件名 - 提取书号
        full_name = input_file.stem
        # 提取书号（假设书号是开头的数字部分）
        import re

        book_number_match = re.match(r"^(\d+)", full_name)
        if book_number_match:
            base_name = book_number_match.group(1)
        else:
            # 如果没有找到书号，使用完整文件名
            base_name = full_name

        result = {}

        # 保存Markdown文件
        if generate_md:
            md_file = output_dir / f"{base_name}.md"
            with open(md_file, "w", encoding="utf-8") as f:
                f.write(markdown_content)
            result["markdown_file"] = str(md_file)

        # 生成HTML文件
        if generate_html:
            # 检查是否包含多篇信息，如果是则分割生成
            if self._has_multiple_chapters(markdown_content):
                result.update(
                    self._generate_split_html(
                        markdown_content, base_name, full_name, output_dir
                    )
                )
            else:
                html_content = self._markdown_to_html(markdown_content, base_name)
                html_file = output_dir / f"{base_name}.html"
                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(html_content)
                result["html_file"] = str(html_file)

        return result

    def _read_template_css(self) -> str:
        """读取书籍模板的CSS样式"""
        css_content = ""
        css_files = ["book/base.css", "book/book.css", "book/index.css"]

        for css_file in css_files:
            css_path = Path(css_file)
            if css_path.exists():
                try:
                    with open(css_path, "r", encoding="utf-8") as f:
                        css_content += f"/* {css_file} */\n"
                        css_content += f.read() + "\n\n"
                except:
                    # 如果读取失败，使用默认样式
                    pass

        # 如果没有找到CSS文件，使用默认样式
        if not css_content:
            css_content = """
body { margin-bottom: 30px }
h1, h2 { color: green; text-align: center }
h1 { font-size:18px }
h2 { font-size:16px }
h3 { font-size:16px; color: red }
h4 { color: red }
#pagefoot { text-align: center }
#nav, .nav { text-align: center }
a[href] { text-decoration: none }
p { margin-bottom: 0px; margin-top: 8px }
#author { text-align: right; color: grey }
"""

        return css_content

    def _has_multiple_chapters(self, markdown_content: str) -> bool:
        """检查是否包含多篇信息或多章内容"""
        # 查找是否有"第X篇"、"第X章"、"说明"、"序"、"简介"等模式
        pattern_chapters = r"^# 第.+篇"
        pattern_sections = r"^# 第.+章"
        pattern_intro = r"^# (说明|序|简介|前言|导言)"

        chapters_matches = re.findall(pattern_chapters, markdown_content, re.MULTILINE)
        sections_matches = re.findall(pattern_sections, markdown_content, re.MULTILINE)
        intro_matches = re.findall(pattern_intro, markdown_content, re.MULTILINE)

        # 如果有说明/序/简介等，加上篇章数量大于等于1就分割
        # 如果没有说明等，篇章数量大于1才分割
        total_chapters = len(chapters_matches) + len(sections_matches)
        if len(intro_matches) > 0:
            return total_chapters >= 1
        else:
            return total_chapters > 1

    def _generate_split_html(
        self, markdown_content: str, base_name: str, full_name: str, output_dir: Path
    ) -> Dict[str, str]:
        """分割生成多个HTML文件"""

        # 创建以文件名为名的子目录
        book_dir = output_dir / base_name
        book_dir.mkdir(parents=True, exist_ok=True)

        result = {}

        # 分割内容
        sections = self._split_markdown_by_chapters(markdown_content)

        # 提取纯书名（去掉书号和作者）
        book_title = self._extract_book_title(full_name)

        # 生成目录页面
        index_content = self._generate_index_html(sections, base_name, book_title)
        index_file = book_dir / "index.htm"
        with open(index_file, "w", encoding="utf-8") as f:
            f.write(index_content)
        result["index_file"] = str(index_file)

        # 检查是否有真正的intro section
        has_real_intro = any(
            section["type"] == "intro" and self._has_real_intro_content(section)
            for section in sections
        )

        # 生成各个章节的HTML文件
        for i, section in enumerate(sections):
            if section["type"] == "intro" and self._has_real_intro_content(section):
                # 只有真正的intro才生成00.htm
                filename = "00.htm"
                nav_content = (
                    f'上一篇 <a href="index.htm">回目录</a> <a href="01.htm">下一篇</a>'
                )
                bottom_nav_content = f'上一篇 <a href="index.htm">回目录</a> <a href="#top">回页首</a> <a href="01.htm">下一篇</a>'
            elif section["type"] == "chapter":
                # 第X篇生成为XX.htm
                filename = f"{section['number']:02d}.htm"

                # 根据是否有真正的intro来决定导航链接
                if has_real_intro:
                    prev_file = (
                        f"{section['number']-1:02d}.htm"
                        if section["number"] > 1
                        else "00.htm"
                    )
                else:
                    prev_file = (
                        f"{section['number']-1:02d}.htm"
                        if section["number"] > 1
                        else None  # 第一篇没有上一篇
                    )

                next_file = (
                    f"{section['number']+1:02d}.htm" if i < len(sections) - 1 else ""
                )

                # 生成导航内容
                prev_link = (
                    f'<a href="{prev_file}">上一篇</a>' if prev_file else "上一篇"
                )
                next_link = (
                    f'<a href="{next_file}">下一篇</a>' if next_file else "下一篇"
                )

                nav_content = f'{prev_link} <a href="index.htm">回目录</a> {next_link}'
                bottom_nav_content = f'{prev_link} <a href="index.htm">回目录</a> <a href="#top">回页首</a> {next_link}'
            else:
                continue

            html_content = self._generate_chapter_html(
                section, nav_content, bottom_nav_content
            )
            html_file = book_dir / filename
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(html_content)
            result[f"chapter_{i}_file"] = str(html_file)

        result["book_directory"] = str(book_dir)
        return result

    def _has_real_intro_content(self, section: dict) -> bool:
        """检查是否有真正的intro内容（不是默认生成的）"""
        if section["type"] != "intro":
            return False

        # 检查标题是否是明确的intro标题（不是默认的"说明"）
        title = section.get("title", "")
        if title in ["序", "简介", "前言", "导言"]:
            return True

        # 检查内容中是否包含明确的intro标记
        content = section.get("content", "").strip()

        # 如果内容为空或只有很少内容，认为不是真正的intro
        if len(content) < 20:
            return False

        intro_patterns = [
            "主后一九",
            "本书是由",
            "这是本书",
            "序言",
            "前言",
            "导言",
            "说明",
        ]

        # 必须包含intro标记才认为是真正的intro
        return any(pattern in content for pattern in intro_patterns)

    def _split_markdown_by_chapters(self, markdown_content: str) -> list:
        """按章节分割Markdown内容"""

        lines = markdown_content.split("\n")
        sections = []
        current_section = {"type": "intro", "title": "说明", "content": "", "number": 0}

        for line in lines:
            # 检查是否是章节标题（篇或章）
            chapter_match = re.match(r"^# 第(.+)篇[　\s]*(.+)", line)
            section_match = re.match(r"^# 第(.+)章[　\s]*(.+)", line)
            intro_match = re.match(r"^# (说明|序|简介|前言|导言)(.*)$", line)

            if chapter_match:
                # 保存前一个章节
                if current_section["content"].strip():
                    sections.append(current_section)

                # 开始新章节（篇）
                chapter_num_text = chapter_match.group(1)
                chapter_title = chapter_match.group(2)

                # 转换中文数字为阿拉伯数字
                chapter_num = self._chinese_to_number(chapter_num_text)

                current_section = {
                    "type": "chapter",
                    "title": f"第{chapter_num_text}篇　{chapter_title}",
                    "content": line + "\n",
                    "number": chapter_num,
                }
            elif section_match:
                # 保存前一个章节
                if current_section["content"].strip():
                    sections.append(current_section)

                # 开始新章节（章）
                section_num_text = section_match.group(1)
                section_title = section_match.group(2)

                # 转换中文数字为阿拉伯数字
                section_num = self._chinese_to_number(section_num_text)

                current_section = {
                    "type": "chapter",
                    "title": f"第{section_num_text}章　{section_title}",
                    "content": line + "\n",
                    "number": section_num,
                }
            elif intro_match:
                # 如果当前section是默认的intro且内容很少，就合并到新的intro中
                if (
                    current_section["type"] == "intro"
                    and len(current_section["content"].strip()) < 50
                ):
                    # 合并内容到新的intro中
                    intro_type = intro_match.group(1)
                    intro_subtitle = intro_match.group(2).strip()

                    title = intro_type
                    if intro_subtitle:
                        title = f"{intro_type}　{intro_subtitle}"

                    current_section = {
                        "type": "intro",
                        "title": title,
                        "content": current_section["content"] + line + "\n",
                        "number": 0,
                    }
                else:
                    # 保存前一个章节
                    if current_section["content"].strip():
                        sections.append(current_section)

                    # 开始新的说明/序/简介章节
                    intro_type = intro_match.group(1)
                    intro_subtitle = intro_match.group(2).strip()

                    title = intro_type
                    if intro_subtitle:
                        title = f"{intro_type}　{intro_subtitle}"

                    current_section = {
                        "type": "intro",
                        "title": title,
                        "content": line + "\n",
                        "number": 0,
                    }
            else:
                current_section["content"] += line + "\n"

        # 添加最后一个章节
        if current_section["content"].strip():
            sections.append(current_section)

        return sections

    def _chinese_to_number(self, chinese_num: str) -> int:
        """将中文数字转换为阿拉伯数字"""
        chinese_map = {
            "一": 1,
            "二": 2,
            "三": 3,
            "四": 4,
            "五": 5,
            "六": 6,
            "七": 7,
            "八": 8,
            "九": 9,
            "十": 10,
        }
        return chinese_map.get(chinese_num, 1)

    def _extract_book_title(self, full_name: str) -> str:
        """从完整文件名中提取纯书名"""
        # 去掉书号（开头的数字）
        title_without_number = re.sub(r"^\d+", "", full_name)

        # 去掉作者（最后的"－作者"部分）
        title_without_author = re.sub(r"[－—-][^－—-]*$", "", title_without_number)

        # 清理可能的多余字符
        clean_title = title_without_author.strip("－—-")

        return clean_title if clean_title else full_name

    def _generate_index_html(
        self, sections: list, base_name: str, book_title: str
    ) -> str:
        """生成目录页面"""
        toc_lines = []
        intro_added = False

        for section in sections:
            if (
                section["type"] == "intro"
                and not intro_added
                and self._has_real_intro_content(section)
            ):
                intro_title = section.get("title", "说明")
                toc_lines.append(f'<p><a href="00.htm">{intro_title}</a></p>')
                intro_added = True
            elif section["type"] == "chapter":
                title = section["title"]
                # 提取篇数/章数和标题
                chapter_match = re.match(r"第(.+)篇[　\s]*(.+)", title)
                section_match = re.match(r"第(.+)章[　\s]*(.+)", title)

                if chapter_match:
                    chapter_num_text = chapter_match.group(1)
                    chapter_title = chapter_match.group(2)
                    filename = f"{section['number']:02d}.htm"
                    toc_lines.append(
                        f'<p>第　{chapter_num_text}篇　<a href="{filename}">{chapter_title}</a></p>'
                    )
                elif section_match:
                    section_num_text = section_match.group(1)
                    section_title = section_match.group(2)
                    filename = f"{section['number']:02d}.htm"
                    toc_lines.append(
                        f'<p>第　{section_num_text}章　<a href="{filename}">{section_title}</a></p>'
                    )

        toc_content = "\n".join(toc_lines)

        template = Template(self.index_template)
        return template.render(
            title=book_title,
            book_title=book_title,
            author="李常受",
            css_content=self.base_css,
            toc_content=toc_content,
        )

    def _generate_chapter_html(
        self, section: dict, nav_content: str, bottom_nav_content: str
    ) -> str:
        """生成章节HTML内容"""
        # 将Markdown转换为HTML
        markdown_content = section["content"]

        # 清理内容
        lines = markdown_content.split("\n")
        cleaned_lines = []

        # 对于intro section，找到实际内容的开始位置
        start_processing = True
        if section["type"] == "intro":
            start_processing = False

        for line in lines:
            line = line.strip()

            # 跳过标题行，因为模板会添加
            if line.startswith("# "):
                if section["type"] == "intro":
                    start_processing = True  # 从说明标题开始处理
                continue

            # 对于intro，只有开始处理后才添加内容
            if section["type"] == "intro" and not start_processing:
                continue

            # 跳过通用的不需要内容
            if line == "目录" or line == "************":
                continue

            # 保留空行以维持段落分割
            if line == "":
                cleaned_lines.append("")
            else:
                cleaned_lines.append(line)

        content_without_title = "\n".join(cleaned_lines).strip()

        # 转换为HTML
        html_body = markdown2.markdown(
            content_without_title,
            extras=[
                "fenced-code-blocks",
                "tables",
                "strike",
                "task_list",
                "code-friendly",
            ],
        )

        # 将内容中的h2标题转换为h3，因为篇题已经使用h2
        html_body = html_body.replace("<h2>", "<h3>").replace("</h2>", "</h3>")

        template = Template(self.chapter_template)
        return template.render(
            title=section["title"],
            css_content=self.base_css,
            nav_content=nav_content,
            bottom_nav_content=bottom_nav_content,
            content=html_body,
        )

    def _docx_to_markdown(self, doc: Document) -> str:
        """将Word文档转换为Markdown格式"""
        markdown_lines = []
        prev_was_list = False

        for paragraph in doc.paragraphs:
            # 跳过空段落
            if not paragraph.text.strip():
                markdown_lines.append("")
                prev_was_list = False
                continue

            # 处理标题
            if paragraph.style.name.startswith("Heading"):
                # 如果前一个是列表项，添加空行来结束列表
                if prev_was_list:
                    markdown_lines.append("")
                level = int(paragraph.style.name.split()[-1])
                markdown_lines.append(f"{'#' * level} {paragraph.text}")
                markdown_lines.append("")  # 标题后添加空行
                prev_was_list = False
            elif paragraph.style.name.startswith(
                "List"
            ) or paragraph.text.strip().startswith(("•", "-", "*")):
                # 处理列表项
                text = self._process_paragraph_formatting(paragraph)
                # 移除开头的列表符号，因为我们会添加markdown格式的符号
                text = text.lstrip("•-* \t")
                markdown_lines.append(f"- {text}")
                prev_was_list = True
            else:
                # 如果前一个是列表项，添加空行来结束列表
                if prev_was_list:
                    markdown_lines.append("")
                # 处理普通段落
                text = self._process_paragraph_formatting(paragraph)
                markdown_lines.append(text)
                markdown_lines.append("")  # 段落后添加空行
                prev_was_list = False

        # 处理表格
        for table in doc.tables:
            markdown_lines.extend(self._process_table(table))
            markdown_lines.append("")  # 表格后添加空行

        # 清理多余的空行
        cleaned_lines = []
        prev_empty = False
        for line in markdown_lines:
            if line.strip() == "":
                if not prev_empty:
                    cleaned_lines.append("")
                prev_empty = True
            else:
                cleaned_lines.append(line)
                prev_empty = False

        return "\n".join(cleaned_lines)

    def _process_paragraph_formatting(self, paragraph) -> str:
        """处理段落中的格式化文本"""
        text_parts = []

        for run in paragraph.runs:
            text = run.text

            # 处理粗体
            if run.bold:
                text = f"**{text}**"

            # 处理斜体
            if run.italic:
                text = f"*{text}*"

            # 处理下划线（转换为强调）
            if run.underline:
                text = f"_{text}_"

            text_parts.append(text)

        return "".join(text_parts)

    def _process_table(self, table) -> list:
        """处理表格转换为Markdown格式"""
        markdown_table = []

        for i, row in enumerate(table.rows):
            row_cells = [cell.text.strip() for cell in row.cells]
            markdown_row = "| " + " | ".join(row_cells) + " |"
            markdown_table.append(markdown_row)

            # 添加表头分隔符
            if i == 0:
                separator = "| " + " | ".join(["---"] * len(row_cells)) + " |"
                markdown_table.append(separator)

        return markdown_table

    def _markdown_to_html(self, markdown_content: str, title: str) -> str:
        """将Markdown转换为HTML"""
        # 使用markdown2转换
        html_body = markdown2.markdown(
            markdown_content,
            extras=[
                "fenced-code-blocks",
                "tables",
                "strike",
                "task_list",
                "code-friendly",
            ],
        )

        # 使用模板生成完整HTML
        template = Template(self.html_template)
        return template.render(title=title, content=html_body)
