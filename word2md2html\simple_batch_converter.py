#!/usr/bin/env python3
"""
Word转HTML简化批量转换器
基于原始converter.py，只添加批量处理功能
"""

import threading
from pathlib import Path
from typing import Dict, List, Union
from concurrent.futures import ThreadPoolExecutor, as_completed

from .converter import Word2Md2HtmlConverter


class SimpleBatchConverter(Word2Md2HtmlConverter):
    """简化的批量转换器，继承原始转换器"""

    def __init__(self):
        """初始化转换器"""
        super().__init__()
        self._lock = threading.Lock()
        self._progress_callback = None
        self._error_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self._progress_callback = callback

    def set_error_callback(self, callback):
        """设置错误回调函数"""
        self._error_callback = callback

    def _report_progress(self, message: str, current: int = 0, total: int = 0):
        """报告进度"""
        if self._progress_callback:
            self._progress_callback(message, current, total)

    def _report_error(self, error: str, file_path: str = ""):
        """报告错误"""
        if self._error_callback:
            self._error_callback(error, file_path)

    def convert_single(
        self,
        input_file: Union[str, Path],
        output_dir: Union[str, Path],
        generate_md: bool = True,
        generate_html: bool = True,
    ) -> Dict[str, str]:
        """转换单个文件（使用父类的convert方法）"""
        # 确保输入文件和输出目录都是Path对象
        input_file = Path(input_file)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        return self.convert(
            input_file=input_file,
            output_dir=output_dir,
            generate_md=generate_md,
            generate_html=generate_html,
        )

    def convert_batch(
        self,
        input_files: List[Union[str, Path]],
        output_dir: Union[str, Path],
        generate_md: bool = True,
        generate_html: bool = True,
        max_workers: int = 4,
    ) -> Dict[str, Dict[str, str]]:
        """批量转换多个文件"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        results = {}
        total_files = len(input_files)
        completed_files = 0

        self._report_progress(f"开始批量转换 {total_files} 个文件...", 0, total_files)

        # 使用线程池进行并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(
                    self._convert_single_with_error_handling,
                    input_file,
                    output_dir,
                    generate_md,
                    generate_html,
                ): input_file
                for input_file in input_files
            }

            # 处理完成的任务
            for future in as_completed(future_to_file):
                input_file = future_to_file[future]
                file_path = Path(input_file)

                try:
                    result = future.result()
                    results[str(file_path)] = result
                    completed_files += 1

                    with self._lock:
                        self._report_progress(
                            f"已完成: {file_path.name}",
                            completed_files,
                            total_files,
                        )

                except Exception as e:
                    with self._lock:
                        self._report_error(str(e), str(file_path))
                        completed_files += 1
                        self._report_progress(
                            f"失败: {file_path.name}",
                            completed_files,
                            total_files,
                        )

        self._report_progress("批量转换完成!", total_files, total_files)

        # 生成总目录页面
        if len(results) > 1:  # 只有多个文件时才生成总目录
            self._generate_master_index(results, output_dir)

        return results

    def _convert_single_with_error_handling(
        self, input_file, output_dir, generate_md, generate_html
    ):
        """带错误处理的单文件转换"""
        try:
            return self.convert_single(
                input_file, output_dir, generate_md, generate_html
            )
        except Exception as e:
            raise Exception(f"转换失败: {e}")

    def convert_directory(
        self,
        input_dir: Union[str, Path],
        output_dir: Union[str, Path],
        pattern: str = "*.docx",
        generate_md: bool = True,
        generate_html: bool = True,
        max_workers: int = 4,
        recursive: bool = False,
    ) -> Dict[str, Dict[str, str]]:
        """转换目录中的所有Word文件"""
        input_dir = Path(input_dir)

        if not input_dir.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")

        # 查找所有Word文件
        if recursive:
            input_files = list(input_dir.rglob(pattern))
        else:
            input_files = list(input_dir.glob(pattern))

        if not input_files:
            raise ValueError(f"在目录 {input_dir} 中没有找到匹配 {pattern} 的文件")

        self._report_progress(f"在目录中找到 {len(input_files)} 个文件")

        results = self.convert_batch(
            input_files, output_dir, generate_md, generate_html, max_workers
        )

        # 目录转换总是生成总目录页面
        if len(results) > 0:
            self._generate_master_index(results, output_dir)

        return results

    def _generate_master_index(
        self, results: Dict[str, Dict[str, str]], output_dir: Path
    ):
        """生成总目录页面"""
        try:
            self._report_progress("生成总目录页面...")

            # 收集所有书籍信息
            books = []
            for file_path, result in results.items():
                if "book_directory" in result:
                    # 从文件路径提取信息
                    input_file = Path(file_path)
                    full_name = input_file.stem

                    # 提取书号
                    import re

                    book_number_match = re.match(r"^(\d+)", full_name)
                    if book_number_match:
                        book_number = book_number_match.group(1)
                    else:
                        book_number = full_name

                    # 提取书名
                    book_title = self._extract_book_title(full_name)

                    # 书籍目录路径
                    book_dir = Path(result["book_directory"])
                    relative_path = book_dir.name + "/index.html"

                    books.append(
                        {
                            "number": book_number,
                            "title": book_title,
                            "full_name": full_name,
                            "path": relative_path,
                        }
                    )

            # 按书号排序
            books.sort(
                key=lambda x: int(x["number"]) if x["number"].isdigit() else 999999
            )

            # 生成HTML内容
            html_content = self._generate_master_index_html(books)

            # 保存总目录文件
            master_index_file = output_dir / "index.html"
            with open(master_index_file, "w", encoding="utf-8") as f:
                f.write(html_content)

            self._report_progress(f"总目录页面已生成: {master_index_file}")

        except Exception as e:
            self._report_error(f"生成总目录页面失败: {e}")

    def _generate_master_index_html(self, books: list) -> str:
        """生成总目录页面的HTML内容"""
        # 使用与书籍目录相同的模板结构
        master_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<style>
{{ css_content }}

/* book/index.css */
p { margin-bottom: 0px; margin-top: 8px }
#author { text-align: right; color: grey }

</style>
<title>{{ title }}</title>
</head>
<body>
<h1>{{ book_title }}</h1>

{{ toc_content }}

<p id="author">{{ author }}</p>
</body>
</html>"""

        # 生成书籍列表HTML（使用与书籍目录相同的格式）
        book_list_html = []
        for book in books:
            book_html = (
                f'<p>{book["number"]}　<a href="{book["path"]}">{book["title"]}</a></p>'
            )
            book_list_html.append(book_html)

        # 渲染模板
        from jinja2 import Template

        template = Template(master_template)
        return template.render(
            title="书籍总目录",
            book_title="书籍总目录",
            css_content=self.base_css,
            toc_content="\n".join(book_list_html),
            author="",  # 移除统计信息和时间戳
        )
